#include "AudioManager.h"

// Static instance for callbacks
AudioManager* AudioManager::instance = nullptr;

// Global audio manager instance
AudioManager audioManager;

AudioManager::AudioManager() {
    source = nullptr;
    decoder = nullptr;
    buffer = nullptr;
    out = nullptr;
    player = nullptr;
    a2dp_source = nullptr;

    current_state = AUDIO_DISCONNECTED;
    connected_device_name = "";
    current_file = "";
    current_volume = 80;
    sd_cs_pin = 5;
    file_count = 0;
    current_file_index = 0;

    audio_task_handle = nullptr;
    audio_task_running = false;
    audio_mutex = nullptr;

    connection_callback = nullptr;
    playback_callback = nullptr;

    instance = this;
}

AudioManager::~AudioManager() {
    stopAudioTask();
    cleanupAudioComponents();

    if (audio_mutex) {
        vSemaphoreDelete(audio_mutex);
        audio_mutex = nullptr;
    }
}

bool AudioManager::init(int sd_cs_pin, const char* device_name) {
    Serial.println("Initializing AudioManager...");

    this->sd_cs_pin = sd_cs_pin;

    // Create mutex for thread synchronization
    audio_mutex = xSemaphoreCreateMutex();
    if (!audio_mutex) {
        Serial.println("Failed to create audio mutex");
        return false;
    }

    // Initialize AudioTools logger
    AudioToolsLogger.begin(Serial, AudioToolsLogLevel::Info);
    
    // Initialize audio components
    if (!initializeAudioComponents()) {
        Serial.println("Failed to initialize audio components");
        return false;
    }
    
    // Initialize A2DP source
    a2dp_source = new BluetoothA2DPSource();
    if (!a2dp_source) {
        Serial.println("Failed to create A2DP source");
        return false;
    }
    
    // Set up A2DP callbacks
    a2dp_source->set_ssid_callback(deviceScanCallback);
    a2dp_source->set_auto_reconnect(false);
    a2dp_source->set_on_connection_state_changed(connectionStateChanged);
    a2dp_source->set_data_callback(getData);
    a2dp_source->set_volume(current_volume);
    
    // Start A2DP with device name
    a2dp_source->start(device_name);
    
    current_state = AUDIO_DISCONNECTED;
    
    Serial.println("AudioManager initialized successfully");
    return true;
}

bool AudioManager::initializeAudioComponents() {
    const int buffer_size = 2 * 1024;
    const char *startFilePath = "/";
    const char *ext = "mp3";

    try {
        Serial.println("Initializing audio components...");

        // Create audio source for SD card
        source = new AudioSourceSDFAT(startFilePath, ext, sd_cs_pin);
        if (!source) {
            Serial.println("Failed to create audio source");
            return false;
        }
        Serial.println("Audio source created");

        // Create MP3 decoder
        decoder = new MP3DecoderHelix();
        if (!decoder) {
            Serial.println("Failed to create MP3 decoder");
            return false;
        }
        Serial.println("MP3 decoder created");

        // Create buffer with proper initialization sequence
        buffer = new BufferRTOS<uint8_t>(0);
        if (!buffer) {
            Serial.println("Failed to create buffer");
            return false;
        }

        // Resize buffer BEFORE creating QueueStream
        Serial.printf("Resizing buffer to %d bytes...\n", buffer_size);
        buffer->resize(buffer_size);
        Serial.println("Buffer resized successfully");

        // Give FreeRTOS time to properly set up the queue
        delay(100);

        // Create queue stream AFTER buffer is properly sized
        out = new QueueStream<uint8_t>(*buffer);
        if (!out) {
            Serial.println("Failed to create queue stream");
            return false;
        }
        Serial.println("Queue stream created");

        // Initialize QueueStream BEFORE creating AudioPlayer
        Serial.println("Initializing queue stream...");
        out->begin(95); // Start when 95% full
        Serial.println("Queue stream initialized");

        // Give additional time for queue initialization
        delay(50);

        // Create audio player AFTER all components are ready
        player = new AudioPlayer(*source, *out, *decoder);
        if (!player) {
            Serial.println("Failed to create audio player");
            return false;
        }
        Serial.println("Audio player created");

        // Configure player settings
        player->setDelayIfOutputFull(0);
        player->setVolume(1);

        // Initialize player LAST
        Serial.println("Initializing audio player...");
        player->begin();
        Serial.println("Audio player initialized");

        // Update file list
        updateFileList();

        Serial.println("All audio components initialized successfully");
        return true;
    } catch (...) {
        Serial.println("Exception during audio component initialization");
        cleanupAudioComponents();
        return false;
    }
}

void AudioManager::cleanupAudioComponents() {
    Serial.println("Cleaning up audio components...");

    // Stop audio task first to prevent access to components being deleted
    if (audio_task_running) {
        stopAudioTask();
    }

    // Clean up in reverse order of creation
    if (player) {
        Serial.println("Deleting audio player...");
        delete player;
        player = nullptr;
    }
    if (out) {
        Serial.println("Deleting queue stream...");
        delete out;
        out = nullptr;
    }
    if (buffer) {
        Serial.println("Deleting buffer...");
        delete buffer;
        buffer = nullptr;
    }
    if (decoder) {
        Serial.println("Deleting MP3 decoder...");
        delete decoder;
        decoder = nullptr;
    }
    if (source) {
        Serial.println("Deleting audio source...");
        delete source;
        source = nullptr;
    }
    if (a2dp_source) {
        Serial.println("Deleting A2DP source...");
        delete a2dp_source;
        a2dp_source = nullptr;
    }

    Serial.println("Audio components cleanup complete");
}

bool AudioManager::startDiscovery() {
    if (!a2dp_source) {
        Serial.println("A2DP source not initialized");
        return false;
    }
    
    Serial.println("Starting Bluetooth device discovery...");
    // The AudioTools A2DP will automatically scan for devices
    return true;
}

bool AudioManager::connectToDevice(const char* device_name) {
    if (!a2dp_source) {
        Serial.println("A2DP source not initialized");
        return false;
    }
    
    Serial.printf("Attempting to connect to device: %s\n", device_name);
    current_state = AUDIO_CONNECTING;
    
    // The connection will be handled by the A2DP source automatically
    // when a matching device is found via the scan callback
    return true;
}

bool AudioManager::isConnected() {
    return a2dp_source && a2dp_source->is_connected();
}

String AudioManager::getConnectedDeviceName() {
    return connected_device_name;
}

bool AudioManager::disconnect() {
    if (!a2dp_source) {
        return false;
    }
    
    a2dp_source->disconnect();
    current_state = AUDIO_DISCONNECTED;
    connected_device_name = "";
    
    if (connection_callback) {
        connection_callback(false, "");
    }
    
    return true;
}

bool AudioManager::play() {
    if (!isConnected() || !player) {
        Serial.println("Cannot play: not connected or player not initialized");
        return false;
    }

    // Take mutex to safely change state
    if (xSemaphoreTake(audio_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        if (current_file.isEmpty() && file_count > 0) {
            // Set first file if none selected
            setCurrentFile(source->toStr());
        }

        if (current_file.isEmpty()) {
            Serial.println("No file to play");
            xSemaphoreGive(audio_mutex);
            return false;
        }

        current_state = AUDIO_PLAYING;
        xSemaphoreGive(audio_mutex);

        if (playback_callback) {
            playback_callback(current_state, current_file.c_str());
        }

        Serial.printf("Playing: %s\n", current_file.c_str());
        return true;
    } else {
        Serial.println("Failed to acquire audio mutex for play");
        return false;
    }
}

bool AudioManager::pause() {
    if (current_state != AUDIO_PLAYING) {
        return false;
    }

    // Take mutex to safely change state
    if (xSemaphoreTake(audio_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        current_state = AUDIO_PAUSED;
        xSemaphoreGive(audio_mutex);

        if (playback_callback) {
            playback_callback(current_state, current_file.c_str());
        }

        Serial.println("Audio paused");
        return true;
    } else {
        Serial.println("Failed to acquire audio mutex for pause");
        return false;
    }
}

bool AudioManager::stop() {
    if (current_state == AUDIO_STOPPED) {
        return true;
    }

    // Take mutex to safely change state
    if (xSemaphoreTake(audio_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        current_state = AUDIO_STOPPED;
        xSemaphoreGive(audio_mutex);

        if (playback_callback) {
            playback_callback(current_state, current_file.c_str());
        }

        Serial.println("Audio stopped");
        return true;
    } else {
        Serial.println("Failed to acquire audio mutex for stop");
        return false;
    }
}

bool AudioManager::nextTrack() {
    Serial.println("Next track requested (not implemented yet)");
    return false;
}

bool AudioManager::previousTrack() {
    Serial.println("Previous track requested (not implemented yet)");
    return false;
}

bool AudioManager::setVolume(int volume) {
    if (volume < 0 || volume > 100) {
        return false;
    }
    
    current_volume = volume;
    
    if (a2dp_source) {
        a2dp_source->set_volume(volume);
    }
    
    Serial.printf("Volume set to: %d\n", volume);
    return true;
}

int AudioManager::getVolume() {
    return current_volume;
}

bool AudioManager::setCurrentFile(const char* filename) {
    if (!filename) {
        return false;
    }
    
    current_file = filename;
    Serial.printf("Current file set to: %s\n", filename);
    return true;
}

String AudioManager::getCurrentFile() {
    return current_file;
}

bool AudioManager::hasNextFile() {
    return file_count > 1;
}

bool AudioManager::hasPreviousFile() {
    return file_count > 1;
}

int AudioManager::getFileCount() {
    return file_count;
}

int AudioManager::getCurrentFileIndex() {
    return current_file_index;
}

AudioState AudioManager::getState() {
    return current_state;
}

bool AudioManager::isPlaying() {
    return current_state == AUDIO_PLAYING;
}

bool AudioManager::isPaused() {
    return current_state == AUDIO_PAUSED;
}

void AudioManager::loop() {
    if (player && (current_state == AUDIO_PLAYING)) {
        // Decode data to buffer
        player->copy();
    }
}

bool AudioManager::startAudioTask() {
    if (audio_task_running) {
        Serial.println("Audio task is already running");
        return true;
    }

    if (!audio_mutex) {
        Serial.println("Audio mutex not initialized - cannot start audio task");
        return false;
    }

    // Create audio processing task on core 0 (opposite from main loop which runs on core 1)
    BaseType_t result = xTaskCreatePinnedToCore(
        audioTaskFunction,      // Task function
        "AudioTask",           // Task name
        4096,                  // Stack size (4KB)
        this,                  // Parameters to pass to task
        3,                     // Task priority (higher than main loop)
        &audio_task_handle,    // Task handle
        0                      // Run on core 0
    );

    if (result == pdPASS) {
        audio_task_running = true;
        Serial.println("Audio task started successfully on core 0");
        return true;
    } else {
        Serial.println("Failed to create audio task");
        return false;
    }
}

void AudioManager::stopAudioTask() {
    if (audio_task_handle && audio_task_running) {
        audio_task_running = false;
        vTaskDelete(audio_task_handle);
        audio_task_handle = nullptr;
        Serial.println("Audio task stopped");
    }
}

bool AudioManager::isAudioTaskRunning() {
    return audio_task_running;
}

void AudioManager::audioTaskFunction(void* parameter) {
    AudioManager* audioManager = static_cast<AudioManager*>(parameter);

    Serial.println("Audio task started - processing audio on dedicated thread");

    while (audioManager->audio_task_running) {
        audioManager->audioLoop();

        // Small delay to prevent task from hogging CPU
        vTaskDelay(pdMS_TO_TICKS(1)); // 1ms delay
    }

    Serial.println("Audio task ending");
    vTaskDelete(nullptr); // Delete self
}

void AudioManager::audioLoop() {
    // Try to take mutex with minimal timeout to avoid blocking audio processing
    if (xSemaphoreTake(audio_mutex, pdMS_TO_TICKS(1)) == pdTRUE) {
        if (player && (current_state == AUDIO_PLAYING)) {
            // Decode data to buffer - this is the main audio processing work
            player->copy();
        }
        xSemaphoreGive(audio_mutex);
    }
    // If we can't get the mutex quickly, skip this iteration to keep audio flowing
}

void AudioManager::setConnectionCallback(void (*callback)(bool connected, const char* device_name)) {
    connection_callback = callback;
}

void AudioManager::setPlaybackCallback(void (*callback)(AudioState state, const char* filename)) {
    playback_callback = callback;
}

void AudioManager::updateFileList() {
    if (!source) {
        file_count = 0;
        return;
    }

    // For now, assume we have files available
    file_count = 1;
    current_file_index = 0;
    current_file = "MP3 from SD card";

    Serial.printf("SD card MP3 source initialized\n");
}

// Static callback methods
int32_t AudioManager::getData(uint8_t *data, int32_t bytes) {
    if (!instance || !instance->buffer || !data || bytes <= 0) {
        return 0;
    }

    // Check if audio task is running and buffer is valid
    if (!instance->audio_task_running) {
        return 0;
    }

    size_t result_bytes = instance->buffer->readArray(data, bytes);
    return result_bytes;
}

bool AudioManager::deviceScanCallback(const char *ssid, esp_bd_addr_t address, int rssi) {
    if (!instance) {
        return false;
    }
    
    Serial.printf("Found device: %s (RSSI: %d)\n", ssid, rssi);
    
    // For now, accept any device that looks like TWS earphones
    // You can customize this logic based on your specific device names
    if (strstr(ssid, "Airdopes") || strstr(ssid, "TWS") || strstr(ssid, "Earbuds") || 
        strstr(ssid, "LEXON") || strstr(ssid, "Buds")) {
        Serial.printf("Accepting device: %s\n", ssid);
        return true;
    }
    
    return false;
}

void AudioManager::connectionStateChanged(esp_a2d_connection_state_t state, void *ptr) {
    if (!instance) {
        return;
    }
    
    switch (state) {
        case ESP_A2D_CONNECTION_STATE_CONNECTED:
            instance->current_state = AUDIO_STOPPED;
            instance->connected_device_name = "Connected Device"; // A2DP doesn't provide device name directly
            Serial.println("Bluetooth A2DP connected");
            if (instance->connection_callback) {
                instance->connection_callback(true, instance->connected_device_name.c_str());
            }
            break;
            
        case ESP_A2D_CONNECTION_STATE_DISCONNECTED:
            instance->current_state = AUDIO_DISCONNECTED;
            instance->connected_device_name = "";
            Serial.println("Bluetooth A2DP disconnected");
            if (instance->connection_callback) {
                instance->connection_callback(false, "");
            }
            break;
            
        case ESP_A2D_CONNECTION_STATE_CONNECTING:
            instance->current_state = AUDIO_CONNECTING;
            Serial.println("Bluetooth A2DP connecting...");
            break;
            
        case ESP_A2D_CONNECTION_STATE_DISCONNECTING:
            Serial.println("Bluetooth A2DP disconnecting...");
            break;
    }
}
